package main

import (
	"context"
	"fmt"
	"log"
	"time"

	logicItem "hallsrv/internal/logic/logic_item"
	modelItem "hallsrv/internal/model/model_item"
	test_init "hallsrv/internal/test"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

func main() {
	// 初始化Redis和Consul连接
	test_init.InitRedisConsul()
	
	ctx := context.Background()
	playerId := uint64(99999)
	itemId := int64(201001)
	
	fmt.Println("=== 道具冷却功能演示 ===")
	
	// 1. 检查道具是否启用冷却
	if modelItem.IsItemCooldownEnabled(itemId) {
		fmt.Printf("道具 %d 已启用冷却功能\n", itemId)
	} else {
		fmt.Printf("道具 %d 未启用冷却功能\n", itemId)
	}
	
	// 2. 获取默认冷却时间
	cooldownTime := modelItem.GetDefaultCooldownTime(itemId)
	fmt.Printf("道具 %d 默认冷却时间: %d 秒\n", itemId, cooldownTime)
	
	// 3. 检查初始冷却状态
	fmt.Println("\n--- 检查初始冷却状态 ---")
	err := logicItem.CheckItemUseCooldown(ctx, playerId, itemId)
	if err != nil {
		fmt.Printf("道具冷却检查失败: %v\n", err)
	} else {
		fmt.Printf("道具 %d 可以使用\n", itemId)
	}
	
	// 4. 设置道具冷却
	fmt.Println("\n--- 设置道具冷却 ---")
	err = logicItem.SetItemUseCooldown(ctx, playerId, itemId)
	if err != nil {
		log.Fatalf("设置道具冷却失败: %v", err)
	}
	fmt.Printf("道具 %d 冷却已设置\n", itemId)
	
	// 5. 再次检查冷却状态
	fmt.Println("\n--- 检查冷却状态 ---")
	err = logicItem.CheckItemUseCooldown(ctx, playerId, itemId)
	if err != nil {
		fmt.Printf("道具冷却检查结果: %v\n", err)
	} else {
		fmt.Printf("道具 %d 可以使用\n", itemId)
	}
	
	// 6. 获取详细冷却信息
	fmt.Println("\n--- 获取详细冷却信息 ---")
	manager := logicItem.GetCooldownManager()
	isReady, cooldownInfo, err := manager.CheckItemCooldown(ctx, playerId, itemId)
	if err != nil {
		log.Fatalf("获取冷却信息失败: %v", err)
	}
	
	fmt.Printf("道具是否可用: %v\n", isReady)
	if cooldownInfo != nil {
		fmt.Printf("冷却信息: 剩余时间 %s, 状态 %d\n", 
			cooldownInfo.GetRemainingTimeStr(), cooldownInfo.Status)
	}
	
	// 7. 批量检查多个道具
	fmt.Println("\n--- 批量检查道具冷却 ---")
	itemIds := []int64{itemId, itemId + 1, itemId + 2}
	
	// 为其中一个道具设置冷却
	err = logicItem.SetItemUseCooldown(ctx, playerId, itemIds[1])
	if err != nil {
		log.Fatalf("设置道具冷却失败: %v", err)
	}
	
	readyMap, cooldownInfoMap, err := logicItem.BatchCheckItemUseCooldown(ctx, playerId, itemIds)
	if err != nil {
		log.Fatalf("批量检查冷却失败: %v", err)
	}
	
	for _, id := range itemIds {
		if readyMap[id] {
			fmt.Printf("道具 %d: 可用\n", id)
		} else {
			if info, exists := cooldownInfoMap[id]; exists {
				fmt.Printf("道具 %d: 冷却中，剩余时间 %s\n", id, info.GetRemainingTimeStr())
			} else {
				fmt.Printf("道具 %d: 冷却中\n", id)
			}
		}
	}
	
	// 8. 获取玩家所有道具冷却状态
	fmt.Println("\n--- 获取玩家所有道具冷却状态 ---")
	allCooldowns, err := logicItem.GetPlayerItemCooldownStatus(ctx, playerId)
	if err != nil {
		log.Fatalf("获取玩家冷却状态失败: %v", err)
	}
	
	if len(allCooldowns) == 0 {
		fmt.Println("玩家没有道具在冷却中")
	} else {
		fmt.Printf("玩家有 %d 个道具在冷却中:\n", len(allCooldowns))
		for id, info := range allCooldowns {
			fmt.Printf("  道具 %d: 剩余时间 %s\n", id, info.GetRemainingTimeStr())
		}
	}
	
	// 9. 演示道具使用前的验证
	fmt.Println("\n--- 演示道具使用前的验证 ---")
	itemList := []*commonPB.ItemBase{
		{ItemId: itemId, ItemCount: 1},
		{ItemId: itemId + 3, ItemCount: 1}, // 这个道具没有冷却
	}
	
	err = logicItem.ValidateItemCooldownBeforeUse(ctx, playerId, itemList)
	if err != nil {
		fmt.Printf("道具使用验证失败: %v\n", err)
	} else {
		fmt.Println("所有道具都可以使用")
	}
	
	// 10. 演示冷却时间过期
	fmt.Println("\n--- 演示短时间冷却 ---")
	shortCooldownItemId := int64(202001)
	
	// 设置一个短时间的冷却（通过直接调用manager）
	err = manager.SetItemCooldown(ctx, playerId, shortCooldownItemId, 3) // 3秒冷却
	if err != nil {
		log.Fatalf("设置短时间冷却失败: %v", err)
	}
	
	fmt.Printf("道具 %d 设置了 3 秒冷却\n", shortCooldownItemId)
	
	// 检查冷却状态
	isReady, cooldownInfo, err = manager.CheckItemCooldown(ctx, playerId, shortCooldownItemId)
	if err != nil {
		log.Fatalf("检查冷却状态失败: %v", err)
	}
	
	if !isReady && cooldownInfo != nil {
		fmt.Printf("道具冷却中，剩余时间: %s\n", cooldownInfo.GetRemainingTimeStr())
	}
	
	// 等待冷却时间过期
	fmt.Println("等待 4 秒...")
	time.Sleep(4 * time.Second)
	
	// 再次检查
	isReady, cooldownInfo, err = manager.CheckItemCooldown(ctx, playerId, shortCooldownItemId)
	if err != nil {
		log.Fatalf("检查冷却状态失败: %v", err)
	}
	
	if isReady {
		fmt.Printf("道具 %d 冷却已完成，可以使用\n", shortCooldownItemId)
	} else {
		fmt.Printf("道具 %d 仍在冷却中\n", shortCooldownItemId)
	}
	
	fmt.Println("\n=== 演示完成 ===")
}
