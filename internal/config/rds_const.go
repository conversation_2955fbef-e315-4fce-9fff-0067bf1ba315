package config

import (
	"fmt"
	"time"
)

// redisKey
const (
	// -----------------  string  -------------------
	// 假日信息(全局变量)
	RDS_KEY_HOLIDAY_INFO = "holiday_info"
	// 玩家月充值总金额(分)p_month_recharge:{month}:{playerId}
	RDS_KEY_MONTH_RECHARGE = "p_month_recharge:%s:%d"
	// CDK日志
	RDS_KEY_CDK_LOG = "cdk_log:%s"
	// CDK批次
	RDS_KEY_CDK_BATCH = "cdk_batch:%d"
	// CDK信息缓存 cdk_info:{cdkCode}
	RDS_KEY_CDK_INFO = "cdk_info:%s"

	// -----------------  hash  -------------------
	// 旅途背包 tbag:{playerId}:{bagType}
	RDS_KEY_TRIP_BAG = "tbag:%d:%d"
	// 旅途钓组 trod:{playerId}
	RDS_KEY_TRIP_ROD = "trod:%d"
	// 玩家bitmap 数据
	RDS_KEY_PLAYER_BITMAP = "p_bitmap:%d"
	// 玩家商品购买次数信息
	RDS_KEY_PLAYER_GOODS_BUY = "goods_buy:%d"
	// 上次游戏信息
	RDS_KEY_LAST_GAME_INFO = "p_last_game:%d"
	// 竿架系统
	RDS_KEY_ROD_RIG_INFO = "rig_info:%d"
	// 玩家道具堆耐久百分比item_heap_durable:{playerId}
	RDS_KEY_ITEM_HEAP_DURABLE = "item_heap_durable:%d"

	// -----------------  zset  -------------------

	// -----------------  lock  -------------------
	// 竿架系统锁key
	RDS_KEY_ROD_RIG_LOCK = "lock:rod_rig:%d"

	// 杆包系统锁
	RDS_KEY_ROD_BAG_LOCK = "lock:bag_rod:%d"

	// CDK兑换锁
	RDS_KEY_CDK_LOCK = "lock:cdk:%s"

	// -----------------  singleflight  -------------------
	// 玩家状态锁
	RDS_CREATE_PLAYER_STATE_BM_SINGLEFLIGHT = "sf:playerState:bm:%d"

	// -----------------  activity 活动  -------------------
	// 连续登录 activity:cl:{playerId}
	RDS_KEY_CONTINUOUS_LOGIN = "act:cl:%d"
)

// redisExpire
const (
	// 旅途背包 1小时
	TRIP_BAG_EXPIRE = 3600 * time.Second
	// 旅途钓组 1小时
	TRIP_ROD_EXPIRE = 3600 * time.Second
	// 玩家商品购买次数信息 30天
	PLAYER_GOODS_BUY_EXPIRE = 30 * 24 * time.Hour
	// 上次游戏信息 7 天
	LAST_GAME_INFO_EXPIRE = 7 * 24 * time.Hour
	// 竿架系统 7 天
	ROD_RIG_INFO_EXPIRE = 7 * 24 * time.Hour
	// 玩家状态 7天
	PLAYER_STATE_BITMAP_EXPIRE = 7 * 24 * time.Hour
	// 玩家道具堆耐久百分比 7天
	ITEM_HEAP_DURABLE_EXPIRE = 7 * 24 * time.Hour
	// 玩家月充值金额过期时间 1个月
	MONTH_RECHARGE_EXPIRE = 30 * 24 * time.Hour
	// CDK缓存过期时间 1天
	CDK_CACHE_EXPIRE = 24 * time.Hour
)

func CreatePlayerStateBmSingleflight(playerId uint64) string {
	return fmt.Sprintf(RDS_CREATE_PLAYER_STATE_BM_SINGLEFLIGHT, playerId)
}

func GetPlayerBitmapRdsKey(playerId uint64) string {
	return fmt.Sprintf(RDS_KEY_PLAYER_BITMAP, playerId)
}

func CdkLockKey(cdkCode string) string {
	return fmt.Sprintf(RDS_KEY_CDK_LOCK, cdkCode)
}

func CdkInfoKey(cdkCode string) string {
	return fmt.Sprintf(RDS_KEY_CDK_INFO, cdkCode)
}
