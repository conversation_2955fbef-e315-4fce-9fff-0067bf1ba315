package proc

import (
	"hallsrv/internal/services"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/gm_handler"
)

func InitGmRpc() {
	gm_handler.InitGmHandler()
	instance := services.GetGmInstance()
	gm_handler.Handler(commonPB.GM_CMD_GC_HALL_ITEM, instance.OperatePlayerItem)
	gm_handler.Handler(commonPB.GM_CMD_GC_HALL_CONTINUOUS_LOGIN, instance.OperateContinuousLogin)
	gm_handler.Handler(commonPB.GM_CMD_GC_HALL_CREATE_CDK, instance.CreateCDKBatchWithCodes)
	gm_handler.Handler(commonPB.GM_CMD_GC_HALL_QUERY_CDK_BATCHES, instance.QueryCDKBatches)
	gm_handler.Handler(commonPB.GM_CMD_GC_HALL_QUERY_CDK_RECORDS, instance.QueryCDKRecords)
	gm_handler.Handler(commonPB.GM_CMD_GC_HALL_DISABLE_CDK_BATCHES, instance.DisableCDKBatch)
}
