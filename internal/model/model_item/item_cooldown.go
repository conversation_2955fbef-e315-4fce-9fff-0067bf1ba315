package modelItem

import (
	"encoding/json"
	"time"

	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
)

// 道具冷却状态常量
const (
	// 道具冷却状态 - 未冷却
	ITEM_COOLDOWN_STATUS_NONE = 0
	// 道具冷却状态 - 冷却中
	ITEM_COOLDOWN_STATUS_COOLING = 1
	// 道具冷却状态 - 冷却完成
	ITEM_COOLDOWN_STATUS_READY = 2
)

// ItemCooldownInfo 道具冷却信息
type ItemCooldownInfo struct {
	PlayerId       uint64 `json:"player_id"`       // 玩家ID
	ItemId         int64  `json:"item_id"`         // 道具ID
	LastUseTime    int64  `json:"last_use_time"`   // 最后使用时间戳
	CooldownTime   int64  `json:"cooldown_time"`   // 冷却时间(秒)
	Status         int32  `json:"status"`          // 冷却状态
	RemainingTime  int64  `json:"remaining_time"`  // 剩余冷却时间(秒)
}

// NewItemCooldownInfo 创建道具冷却信息
func NewItemCooldownInfo(playerId uint64, itemId int64, cooldownTime int64) *ItemCooldownInfo {
	now := timex.Now().Unix()
	return &ItemCooldownInfo{
		PlayerId:      playerId,
		ItemId:        itemId,
		LastUseTime:   now,
		CooldownTime:  cooldownTime,
		Status:        ITEM_COOLDOWN_STATUS_COOLING,
		RemainingTime: cooldownTime,
	}
}

// UpdateCooldownStatus 更新冷却状态
func (info *ItemCooldownInfo) UpdateCooldownStatus() {
	if info == nil {
		return
	}

	now := timex.Now().Unix()
	elapsed := now - info.LastUseTime
	
	if elapsed >= info.CooldownTime {
		info.Status = ITEM_COOLDOWN_STATUS_READY
		info.RemainingTime = 0
	} else {
		info.Status = ITEM_COOLDOWN_STATUS_COOLING
		info.RemainingTime = info.CooldownTime - elapsed
	}
}

// IsReady 检查是否冷却完成
func (info *ItemCooldownInfo) IsReady() bool {
	if info == nil {
		return true
	}
	
	info.UpdateCooldownStatus()
	return info.Status == ITEM_COOLDOWN_STATUS_READY
}

// SetLastUseTime 设置最后使用时间
func (info *ItemCooldownInfo) SetLastUseTime(useTime int64) {
	if info == nil {
		return
	}
	
	info.LastUseTime = useTime
	info.Status = ITEM_COOLDOWN_STATUS_COOLING
	info.RemainingTime = info.CooldownTime
}

// ToJSON 序列化为JSON字符串
func (info *ItemCooldownInfo) ToJSON() (string, error) {
	if info == nil {
		return "", nil
	}
	
	data, err := json.Marshal(info)
	if err != nil {
		return "", err
	}
	
	return string(data), nil
}

// NewItemCooldownInfoFromJSON 从JSON字符串反序列化
func NewItemCooldownInfoFromJSON(jsonStr string) (*ItemCooldownInfo, error) {
	if jsonStr == "" {
		return nil, nil
	}
	
	info := &ItemCooldownInfo{}
	err := json.Unmarshal([]byte(jsonStr), info)
	if err != nil {
		return nil, err
	}
	
	// 更新状态
	info.UpdateCooldownStatus()
	
	return info, nil
}

// GetRemainingTimeStr 获取剩余时间的友好显示字符串
func (info *ItemCooldownInfo) GetRemainingTimeStr() string {
	if info == nil || info.IsReady() {
		return "0s"
	}
	
	duration := time.Duration(info.RemainingTime) * time.Second
	
	if duration >= time.Hour {
		return duration.Truncate(time.Minute).String()
	} else if duration >= time.Minute {
		return duration.Truncate(time.Second).String()
	} else {
		return duration.String()
	}
}

// ItemCooldownConfig 道具冷却配置
type ItemCooldownConfig struct {
	ItemId       int64 `json:"item_id"`       // 道具ID
	CooldownTime int64 `json:"cooldown_time"` // 冷却时间(秒)
	Enabled      bool  `json:"enabled"`       // 是否启用冷却
}

// GetDefaultCooldownTime 获取默认冷却时间(秒)
// 可以根据道具类型返回不同的默认冷却时间
func GetDefaultCooldownTime(itemId int64) int64 {
	// 默认冷却时间为5分钟
	// 后续可以根据配置文件或数据库配置来动态获取
	return 5 * 60
}

// IsItemCooldownEnabled 检查道具是否启用冷却功能
// 可以根据道具类型或配置来判断
func IsItemCooldownEnabled(itemId int64) bool {
	// 默认启用冷却功能
	// 后续可以根据配置文件或数据库配置来动态判断
	return true
}
