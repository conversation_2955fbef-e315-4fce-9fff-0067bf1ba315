package daoItemCooldown

import (
	"context"
	"errors"
	"fmt"
	"hallsrv/internal/config"
	modelItem "hallsrv/internal/model/model_item"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
	"github.com/go-redis/redis/v8"
)

// GetRedisCli 获取Redis客户端
func GetRedisCli() *redisx.Client {
	return redisx.GetGeneralCli()
}

// SetItemCooldown 设置道具冷却时间
func SetItemCooldown(ctx context.Context, playerId uint64, itemId int64, cooldownTime int64) error {
	entry := logx.NewLogEntry(ctx)
	
	if playerId == 0 || itemId == 0 || cooldownTime <= 0 {
		return fmt.Errorf("invalid parameters: playerId=%d, itemId=%d, cooldownTime=%d", playerId, itemId, cooldownTime)
	}
	
	// 创建冷却信息
	cooldownInfo := modelItem.NewItemCooldownInfo(playerId, itemId, cooldownTime)
	
	// 序列化为JSON
	jsonData, err := cooldownInfo.ToJSON()
	if err != nil {
		entry.Errorf("serialize cooldown info failed: playerId=%d, itemId=%d, err=%v", playerId, itemId, err)
		return err
	}
	
	// 存储到Redis
	key := config.ItemCooldownKey(playerId, itemId)
	err = GetRedisCli().Set(ctx, key, jsonData, config.ITEM_COOLDOWN_EXPIRE).Err()
	if err != nil {
		entry.Errorf("set item cooldown failed: playerId=%d, itemId=%d, err=%v", playerId, itemId, err)
		return err
	}
	
	entry.Debugf("set item cooldown success: playerId=%d, itemId=%d, cooldownTime=%d", playerId, itemId, cooldownTime)
	return nil
}

// GetItemCooldown 获取道具冷却信息
func GetItemCooldown(ctx context.Context, playerId uint64, itemId int64) (*modelItem.ItemCooldownInfo, error) {
	entry := logx.NewLogEntry(ctx)
	
	if playerId == 0 || itemId == 0 {
		return nil, fmt.Errorf("invalid parameters: playerId=%d, itemId=%d", playerId, itemId)
	}
	
	key := config.ItemCooldownKey(playerId, itemId)
	jsonData, err := GetRedisCli().Get(ctx, key).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			// 没有冷却记录，返回nil表示没有冷却
			return nil, nil
		}
		entry.Errorf("get item cooldown failed: playerId=%d, itemId=%d, err=%v", playerId, itemId, err)
		return nil, err
	}
	
	// 反序列化
	cooldownInfo, err := modelItem.NewItemCooldownInfoFromJSON(jsonData)
	if err != nil {
		entry.Errorf("deserialize cooldown info failed: playerId=%d, itemId=%d, err=%v", playerId, itemId, err)
		return nil, err
	}
	
	return cooldownInfo, nil
}

// CheckItemCooldown 检查道具是否在冷却中
func CheckItemCooldown(ctx context.Context, playerId uint64, itemId int64) (bool, *modelItem.ItemCooldownInfo, error) {
	entry := logx.NewLogEntry(ctx)
	
	// 检查道具是否启用冷却功能
	if !modelItem.IsItemCooldownEnabled(itemId) {
		return true, nil, nil // 未启用冷却，直接返回可用
	}
	
	cooldownInfo, err := GetItemCooldown(ctx, playerId, itemId)
	if err != nil {
		entry.Errorf("check item cooldown failed: playerId=%d, itemId=%d, err=%v", playerId, itemId, err)
		return false, nil, err
	}
	
	// 没有冷却记录，表示可以使用
	if cooldownInfo == nil {
		return true, nil, nil
	}
	
	// 检查是否冷却完成
	isReady := cooldownInfo.IsReady()
	
	entry.Debugf("check item cooldown: playerId=%d, itemId=%d, isReady=%v, remaining=%d", 
		playerId, itemId, isReady, cooldownInfo.RemainingTime)
	
	return isReady, cooldownInfo, nil
}

// DeleteItemCooldown 删除道具冷却记录
func DeleteItemCooldown(ctx context.Context, playerId uint64, itemId int64) error {
	entry := logx.NewLogEntry(ctx)
	
	if playerId == 0 || itemId == 0 {
		return fmt.Errorf("invalid parameters: playerId=%d, itemId=%d", playerId, itemId)
	}
	
	key := config.ItemCooldownKey(playerId, itemId)
	err := GetRedisCli().Del(ctx, key).Err()
	if err != nil {
		entry.Errorf("delete item cooldown failed: playerId=%d, itemId=%d, err=%v", playerId, itemId, err)
		return err
	}
	
	entry.Debugf("delete item cooldown success: playerId=%d, itemId=%d", playerId, itemId)
	return nil
}

// UpdateItemLastUseTime 更新道具最后使用时间
func UpdateItemLastUseTime(ctx context.Context, playerId uint64, itemId int64) error {
	entry := logx.NewLogEntry(ctx)
	
	// 获取当前冷却信息
	cooldownInfo, err := GetItemCooldown(ctx, playerId, itemId)
	if err != nil {
		return err
	}
	
	// 如果没有冷却记录，创建新的
	if cooldownInfo == nil {
		cooldownTime := modelItem.GetDefaultCooldownTime(itemId)
		return SetItemCooldown(ctx, playerId, itemId, cooldownTime)
	}
	
	// 更新最后使用时间
	cooldownInfo.SetLastUseTime(timex.Now().Unix())
	
	// 序列化并保存
	jsonData, err := cooldownInfo.ToJSON()
	if err != nil {
		entry.Errorf("serialize cooldown info failed: playerId=%d, itemId=%d, err=%v", playerId, itemId, err)
		return err
	}
	
	key := config.ItemCooldownKey(playerId, itemId)
	err = GetRedisCli().Set(ctx, key, jsonData, config.ITEM_COOLDOWN_EXPIRE).Err()
	if err != nil {
		entry.Errorf("update item last use time failed: playerId=%d, itemId=%d, err=%v", playerId, itemId, err)
		return err
	}
	
	entry.Debugf("update item last use time success: playerId=%d, itemId=%d", playerId, itemId)
	return nil
}

// BatchCheckItemCooldown 批量检查道具冷却状态
func BatchCheckItemCooldown(ctx context.Context, playerId uint64, itemIds []int64) (map[int64]bool, error) {
	entry := logx.NewLogEntry(ctx)
	
	if playerId == 0 || len(itemIds) == 0 {
		return nil, fmt.Errorf("invalid parameters: playerId=%d, itemIds length=%d", playerId, len(itemIds))
	}
	
	result := make(map[int64]bool)
	
	for _, itemId := range itemIds {
		isReady, _, err := CheckItemCooldown(ctx, playerId, itemId)
		if err != nil {
			entry.Errorf("batch check item cooldown failed: playerId=%d, itemId=%d, err=%v", playerId, itemId, err)
			result[itemId] = false // 出错时默认为不可用
		} else {
			result[itemId] = isReady
		}
	}
	
	return result, nil
}

// GetPlayerAllItemCooldowns 获取玩家所有道具的冷却信息
func GetPlayerAllItemCooldowns(ctx context.Context, playerId uint64) (map[int64]*modelItem.ItemCooldownInfo, error) {
	entry := logx.NewLogEntry(ctx)
	
	if playerId == 0 {
		return nil, fmt.Errorf("invalid playerId: %d", playerId)
	}
	
	// 使用通配符查找所有相关的key
	pattern := fmt.Sprintf("item_cooldown:%d:*", playerId)
	keys, err := GetRedisCli().Keys(ctx, pattern).Result()
	if err != nil {
		entry.Errorf("get player all item cooldowns keys failed: playerId=%d, err=%v", playerId, err)
		return nil, err
	}
	
	result := make(map[int64]*modelItem.ItemCooldownInfo)
	
	for _, key := range keys {
		jsonData, err := GetRedisCli().Get(ctx, key).Result()
		if err != nil {
			entry.Warnf("get cooldown data failed: key=%s, err=%v", key, err)
			continue
		}
		
		cooldownInfo, err := modelItem.NewItemCooldownInfoFromJSON(jsonData)
		if err != nil {
			entry.Warnf("deserialize cooldown info failed: key=%s, err=%v", key, err)
			continue
		}
		
		if cooldownInfo != nil {
			result[cooldownInfo.ItemId] = cooldownInfo
		}
	}
	
	return result, nil
}
