package dao_cdk

import (
	"context"
	"errors"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"hallsrv/internal/config"
	"hallsrv/internal/model/model_cdk"
)

func GetRedisCli() *redisx.Client {
	return redisx.GetGeneralCli()
}

// GetCdkInfoFromCache 从缓存中获取CDK信息
func GetCdkInfoFromCache(ctx context.Context, cdkCode string) (*model_cdk.CdkBatchRecordResult, error) {
	key := config.CdkInfoKey(cdkCode)
	data, err := GetRedisCli().Get(ctx, key).Result()
	if err != nil {
		return nil, err
	}

	// 使用model层的反序列化方法
	return model_cdk.NewCdkBatchRecordResultFromJSON(data)
}

// SetCdkInfoToCache 将CDK信息存入缓存
func SetCdkInfoToCache(ctx context.Context, info *model_cdk.CdkBatchRecordResult) error {
	if info == nil || info.Cdk == "" {
		return errors.New("cdk info is nil")
	}

	entry := logx.NewLogEntry(ctx)

	// 使用model层的序列化方法
	data, err := info.ToJSON()
	if err != nil {
		return err
	}

	// 存入缓存
	key := config.CdkInfoKey(info.Cdk)
	err = GetRedisCli().Set(ctx, key, data, config.CDK_CACHE_EXPIRE).Err()
	if err != nil {
		entry.Errorf("set cache failed: cdk=%s err=%v", info.Cdk, err)
		return err
	}

	return nil
}

// DeleteCdkInfoFromCache 从缓存中删除CDK信息
func DeleteCdkInfoFromCache(ctx context.Context, cdkCode string) error {
	entry := logx.NewLogEntry(ctx)

	key := config.CdkInfoKey(cdkCode)
	_, err := GetRedisCli().Del(ctx, key).Result()
	if err != nil {
		entry.Errorf("delete cache failed: cdk=%s err=%v", cdkCode, err)
		return err
	}

	return nil
}
