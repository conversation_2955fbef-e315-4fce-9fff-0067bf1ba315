package services

import (
	"context"
	logicItem "hallsrv/internal/logic/logic_item"
	modelItem "hallsrv/internal/model/model_item"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	hallPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/hall"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
)

// ItemCooldownService 道具冷却服务
type ItemCooldownService struct{}

// NewItemCooldownService 创建道具冷却服务
func NewItemCooldownService() *ItemCooldownService {
	return &ItemCooldownService{}
}

// GetPlayerItemCooldowns 获取玩家道具冷却状态
func (s *ItemCooldownService) GetPlayerItemCooldowns(ctx context.Context, req *hallPB.GetPlayerItemCooldownsReq) (*hallPB.GetPlayerItemCooldownsRsp, error) {
	entry := logx.NewLogEntry(ctx)
	
	// 获取玩家ID
	playerId := interceptor.GetRPCOptions(ctx).PlayerId
	if playerId == 0 {
		entry.Errorf("invalid playerId: %d", playerId)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "invalid playerId")
	}
	
	// 获取玩家道具冷却状态
	cooldownMap, err := logicItem.GetPlayerItemCooldownStatus(ctx, playerId)
	if err != nil {
		entry.Errorf("get player item cooldowns failed: playerId=%d, err=%v", playerId, err)
		return nil, err
	}
	
	// 转换为协议格式
	cooldownList := make([]*hallPB.ItemCooldownInfo, 0, len(cooldownMap))
	for itemId, cooldownInfo := range cooldownMap {
		if cooldownInfo != nil {
			pbInfo := &hallPB.ItemCooldownInfo{
				ItemId:        itemId,
				LastUseTime:   cooldownInfo.LastUseTime,
				CooldownTime:  cooldownInfo.CooldownTime,
				RemainingTime: cooldownInfo.RemainingTime,
				Status:        cooldownInfo.Status,
			}
			cooldownList = append(cooldownList, pbInfo)
		}
	}
	
	rsp := &hallPB.GetPlayerItemCooldownsRsp{
		CooldownList: cooldownList,
	}
	
	entry.Debugf("get player item cooldowns success: playerId=%d, count=%d", playerId, len(cooldownList))
	return rsp, nil
}

// CheckItemCooldown 检查单个道具冷却状态
func (s *ItemCooldownService) CheckItemCooldown(ctx context.Context, req *hallPB.CheckItemCooldownReq) (*hallPB.CheckItemCooldownRsp, error) {
	entry := logx.NewLogEntry(ctx)
	
	// 获取玩家ID
	playerId := interceptor.GetRPCOptions(ctx).PlayerId
	if playerId == 0 {
		entry.Errorf("invalid playerId: %d", playerId)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "invalid playerId")
	}
	
	if req.ItemId == 0 {
		entry.Errorf("invalid itemId: %d", req.ItemId)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "invalid itemId")
	}
	
	// 检查道具冷却状态
	manager := logicItem.GetCooldownManager()
	isReady, cooldownInfo, err := manager.CheckItemCooldown(ctx, playerId, req.ItemId)
	if err != nil {
		entry.Errorf("check item cooldown failed: playerId=%d, itemId=%d, err=%v", playerId, req.ItemId, err)
		return nil, err
	}
	
	rsp := &hallPB.CheckItemCooldownRsp{
		ItemId:  req.ItemId,
		IsReady: isReady,
	}
	
	// 如果有冷却信息，添加到响应中
	if cooldownInfo != nil {
		rsp.CooldownInfo = &hallPB.ItemCooldownInfo{
			ItemId:        cooldownInfo.ItemId,
			LastUseTime:   cooldownInfo.LastUseTime,
			CooldownTime:  cooldownInfo.CooldownTime,
			RemainingTime: cooldownInfo.RemainingTime,
			Status:        cooldownInfo.Status,
		}
	}
	
	entry.Debugf("check item cooldown success: playerId=%d, itemId=%d, isReady=%v", playerId, req.ItemId, isReady)
	return rsp, nil
}

// BatchCheckItemCooldown 批量检查道具冷却状态
func (s *ItemCooldownService) BatchCheckItemCooldown(ctx context.Context, req *hallPB.BatchCheckItemCooldownReq) (*hallPB.BatchCheckItemCooldownRsp, error) {
	entry := logx.NewLogEntry(ctx)
	
	// 获取玩家ID
	playerId := interceptor.GetRPCOptions(ctx).PlayerId
	if playerId == 0 {
		entry.Errorf("invalid playerId: %d", playerId)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "invalid playerId")
	}
	
	if len(req.ItemIds) == 0 {
		entry.Errorf("empty itemIds list")
		return nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "empty itemIds list")
	}
	
	// 批量检查道具冷却状态
	readyMap, cooldownInfoMap, err := logicItem.BatchCheckItemUseCooldown(ctx, playerId, req.ItemIds)
	if err != nil {
		entry.Errorf("batch check item cooldown failed: playerId=%d, err=%v", playerId, err)
		return nil, err
	}
	
	// 转换为协议格式
	resultList := make([]*hallPB.ItemCooldownResult, 0, len(req.ItemIds))
	for _, itemId := range req.ItemIds {
		result := &hallPB.ItemCooldownResult{
			ItemId:  itemId,
			IsReady: readyMap[itemId],
		}
		
		// 如果有冷却信息，添加到结果中
		if cooldownInfo, exists := cooldownInfoMap[itemId]; exists && cooldownInfo != nil {
			result.CooldownInfo = &hallPB.ItemCooldownInfo{
				ItemId:        cooldownInfo.ItemId,
				LastUseTime:   cooldownInfo.LastUseTime,
				CooldownTime:  cooldownInfo.CooldownTime,
				RemainingTime: cooldownInfo.RemainingTime,
				Status:        cooldownInfo.Status,
			}
		}
		
		resultList = append(resultList, result)
	}
	
	rsp := &hallPB.BatchCheckItemCooldownRsp{
		Results: resultList,
	}
	
	entry.Debugf("batch check item cooldown success: playerId=%d, count=%d", playerId, len(resultList))
	return rsp, nil
}

// SetItemCooldownConfig 设置道具冷却配置（管理员接口）
func (s *ItemCooldownService) SetItemCooldownConfig(ctx context.Context, req *hallPB.SetItemCooldownConfigReq) (*hallPB.SetItemCooldownConfigRsp, error) {
	entry := logx.NewLogEntry(ctx)
	
	if req.ItemId == 0 {
		entry.Errorf("invalid itemId: %d", req.ItemId)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "invalid itemId")
	}
	
	if req.CooldownTime < 0 {
		entry.Errorf("invalid cooldownTime: %d", req.CooldownTime)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "invalid cooldownTime")
	}
	
	// TODO: 这里可以实现动态配置道具冷却时间的逻辑
	// 目前先返回成功，后续可以扩展为从数据库或配置中心读取/设置配置
	
	rsp := &hallPB.SetItemCooldownConfigRsp{
		Success: true,
		Message: "配置设置成功",
	}
	
	entry.Debugf("set item cooldown config success: itemId=%d, cooldownTime=%d, enabled=%v", 
		req.ItemId, req.CooldownTime, req.Enabled)
	return rsp, nil
}

// GetItemCooldownConfig 获取道具冷却配置
func (s *ItemCooldownService) GetItemCooldownConfig(ctx context.Context, req *hallPB.GetItemCooldownConfigReq) (*hallPB.GetItemCooldownConfigRsp, error) {
	entry := logx.NewLogEntry(ctx)
	
	if req.ItemId == 0 {
		entry.Errorf("invalid itemId: %d", req.ItemId)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "invalid itemId")
	}
	
	// 获取道具冷却配置
	cooldownTime := modelItem.GetDefaultCooldownTime(req.ItemId)
	enabled := modelItem.IsItemCooldownEnabled(req.ItemId)
	
	rsp := &hallPB.GetItemCooldownConfigRsp{
		ItemId:       req.ItemId,
		CooldownTime: cooldownTime,
		Enabled:      enabled,
	}
	
	entry.Debugf("get item cooldown config success: itemId=%d, cooldownTime=%d, enabled=%v", 
		req.ItemId, cooldownTime, enabled)
	return rsp, nil
}
