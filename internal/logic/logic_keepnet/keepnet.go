package logicKeepnet

import (
	"context"
	"fmt"
	daoKeepnet "spotsrv/internal/dao/dao_keepnet"

	modelKeepnet "spotsrv/internal/model/model_keepnet"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
)

// PlayerFishKeepnetOpt 玩家鱼护操作
func PlayerFishKeepnetOpt(ctx context.Context, playerId uint64, fishInstance string, optType commonPB.FISH_KEEPNET_OPT_TYPE) (int32, error) {
	entry := logx.NewLogEntry(ctx)
	fishWeight, err := KeepnetOpt(ctx, playerId, fishInstance, optType)
	if err != nil {
		return 0, err
	}

	// 转化为int32
	if intWeight, ok := fishWeight.(int32); ok {
		return intWeight, nil
	}

	entry.Errorf("logicKeepnet.PlayerFishKeepnetOpt fishWeight is not int, playerId:%d, fishInstance:%s, optType:%d", playerId, fishInstance, optType)

	return 0, fmt.Errorf("unexpected type for fishWeight")
}

// GetPlayerKeepnetFishInfo 查询鱼护鱼详细信息
func GetPlayerKeepnetFishInfo(ctx context.Context, playerId uint64) (map[string]*modelKeepnet.FishDetailInfo, error) {
	entry := logx.NewLogEntry(ctx)
	mapKeepnetInfo, err := daoKeepnet.GetPlayerKeepnetFish(ctx, playerId)
	if err != nil {
		entry.Errorf("query player keepnet info error, playerId:%d, error:%v", playerId, err)
		return nil, err
	}

	//转化成FishKeepnet 添加新鲜度
	fishKeepnet := modelKeepnet.NewFishKeepnetFromRedisHash(ctx, mapKeepnetInfo)

	return fishKeepnet.FishList, nil
}
