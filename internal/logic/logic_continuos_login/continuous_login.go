package logic_continuos_login

import (
	"context"
	logicItem "hallsrv/internal/logic/logic_item"
	"hallsrv/internal/model/model_continuous_login"
	"hallsrv/internal/repository"
	"time"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
)

// GetContinuousLogin 查询活动信息
func GetContinuousLogin(ctx context.Context, playerId uint64) (*model_continuous_login.TConstinuousLogin, error) {

	data, err := repository.GetContinuousLogin(ctx, playerId)
	if err != nil {
		return nil, err
	}

	return data, nil
}

// RewardContinuosLogin 领取奖励
func RewardContinuosLogin(ctx context.Context, playerId uint64) (*model_continuous_login.TConstinuousLogin, *commonPB.Reward, error) {
	data, err := repository.GetContinuousLogin(ctx, playerId)
	if err != nil {
		return nil, nil, err
	}
	// 判断是否领取过
	if data.Day != 0 {
		timexx := timex.NewOperateTime(time.Unix(data.UpdateAt, 0))
		nextDay := timexx.GetNextDay(1).Unix()
		if nextDay > timex.Now().Unix() {
			// logrus.Debugf("now:%+v nextDay:%+v", timex.Now(), nextDay)
			return data, nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "cond not enought")
		}
	}

	config := cmodel.GetAllContinuousLogin()
	awardList := make([]*commonPB.ItemBase, 0)
	for _, cfg := range config {
		if cfg.Day == data.Day+1 {
			for _, item := range cfg.Award {
				awardList = append(awardList, &commonPB.ItemBase{
					ItemId:    item.ItemId,
					ItemCount: item.ItemVal,
				})
			}
			break
		}
	}
	if len(awardList) == 0 {
		return data, nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "reward not found")
	}
	// 更新标签
	data, err = repository.UpdateContinuousLogin(ctx, playerId, data.Day+1)
	if err != nil {
		return data, nil, err
	}

	// 发放奖励
	reward, err := logicItem.OperatePlayerItem(ctx, playerId, awardList, commonPB.ITEM_OPERATION_IO_ADD, commonPB.ITEM_SOURCE_TYPE_IST_ACTIVITY_CONTINUOUS_LOGIN, commonPB.STORAGE_TYPE_ST_STORE, false)
	if err != nil {
		return data, nil, err
	}

	return data, reward, nil
}
