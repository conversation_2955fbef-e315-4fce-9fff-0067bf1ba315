# 道具使用冷却功能设计文档

## 功能概述

为 `logic_item` 模块添加道具使用冷却功能，参考 `logic_keepnet` 模块的实现模式，提供完整的道具冷却管理能力。

## 技术架构

### 1. 分层设计

遵循Clean Architecture模式，实现完整的调用链：

```
Handler -> Service -> Logic -> DAO -> Redis
```

### 2. 模块结构

```
internal/
├── config/
│   └── rds_const.go                    # Redis配置常量
├── model/model_item/
│   └── item_cooldown.go               # 冷却数据模型
├── dao/dao_item_cooldown/
│   └── item_cooldown_rds.go           # Redis操作层
├── logic/logic_item/
│   ├── item_cooldown.go               # 冷却业务逻辑
│   ├── item_cooldown_test.go          # 单元测试
│   └── item.go                        # 集成到现有道具操作
└── services/
    └── item_cooldown_service.go       # 服务层接口
```

## 核心功能

### 1. 冷却检查
- 道具使用前自动检查冷却状态
- 支持单个和批量道具冷却检查
- 冷却中的道具会阻止使用并返回错误信息

### 2. 冷却设置
- 道具使用后自动设置冷却时间
- 支持不同道具配置不同的冷却时间
- 异步设置，不影响主流程性能

### 3. 冷却管理
- 提供统一的冷却管理接口
- 支持获取玩家所有道具的冷却状态
- 支持动态配置道具冷却参数

## 使用方式

### 1. 自动集成

道具冷却功能已自动集成到现有的道具操作流程中：

```go
// 道具使用时会自动检查和设置冷却
rewardInfo, err := logicItem.OperatePlayerItem(ctx, playerId, itemList, 
    commonPB.ITEM_OPERATION_IO_USE, srcType, storeType, isUnpack)
```

### 2. 手动检查

```go
// 检查单个道具冷却状态
err := logicItem.CheckItemUseCooldown(ctx, playerId, itemId)
if err != nil {
    // 道具在冷却中
    return err
}

// 批量检查道具冷却状态
readyMap, cooldownInfoMap, err := logicItem.BatchCheckItemUseCooldown(ctx, playerId, itemIds)
```

### 3. 获取冷却信息

```go
// 获取玩家所有道具的冷却状态
cooldownMap, err := logicItem.GetPlayerItemCooldownStatus(ctx, playerId)
for itemId, cooldownInfo := range cooldownMap {
    fmt.Printf("道具 %d 剩余冷却时间: %s\n", itemId, cooldownInfo.GetRemainingTimeStr())
}
```

## 配置说明

### 1. Redis配置

```go
// Redis Key格式
RDS_KEY_ITEM_COOLDOWN = "item_cooldown:%d:%d"  // item_cooldown:{playerId}:{itemId}

// 过期时间
ITEM_COOLDOWN_EXPIRE = 24 * time.Hour  // 24小时
```

### 2. 冷却时间配置

```go
// 获取默认冷却时间（可扩展为配置文件或数据库配置）
func GetDefaultCooldownTime(itemId int64) int64 {
    // 默认5分钟冷却
    return 5 * 60
}

// 检查道具是否启用冷却（可扩展为配置文件或数据库配置）
func IsItemCooldownEnabled(itemId int64) bool {
    // 默认启用
    return true
}
```

## 数据结构

### 1. 冷却信息模型

```go
type ItemCooldownInfo struct {
    PlayerId       uint64 `json:"player_id"`       // 玩家ID
    ItemId         int64  `json:"item_id"`         // 道具ID
    LastUseTime    int64  `json:"last_use_time"`   // 最后使用时间戳
    CooldownTime   int64  `json:"cooldown_time"`   // 冷却时间(秒)
    Status         int32  `json:"status"`          // 冷却状态
    RemainingTime  int64  `json:"remaining_time"`  // 剩余冷却时间(秒)
}
```

### 2. 冷却状态常量

```go
const (
    ITEM_COOLDOWN_STATUS_NONE    = 0  // 未冷却
    ITEM_COOLDOWN_STATUS_COOLING = 1  // 冷却中
    ITEM_COOLDOWN_STATUS_READY   = 2  // 冷却完成
)
```

## 错误处理

### 1. 冷却错误

```go
// 道具在冷却中时返回的错误
return protox.CodeError(commonPB.ErrCode_ERR_ITEM_USE_LIMIT, 
    fmt.Sprintf("道具冷却中，剩余时间: %s", cooldownInfo.GetRemainingTimeStr()))
```

### 2. 参数错误

```go
// 参数验证失败
return protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "invalid parameters")
```

## 性能优化

### 1. 异步处理
- 冷却设置采用异步方式，不阻塞主流程
- 批量操作减少Redis访问次数

### 2. 缓存策略
- 使用Redis存储冷却信息，支持过期自动清理
- 冷却状态实时计算，避免定时任务开销

### 3. 并发安全
- 使用Redis原子操作保证并发安全
- 避免竞态条件和数据不一致

## 扩展性设计

### 1. 配置扩展
- 支持从配置文件或数据库动态读取冷却配置
- 支持不同道具类型配置不同的冷却策略

### 2. 功能扩展
- 预留接口支持更复杂的冷却策略（如递减冷却、条件冷却等）
- 支持冷却时间的动态调整

### 3. 监控扩展
- 预留日志和监控接口
- 支持冷却使用情况的统计分析

## 测试

### 1. 单元测试

```bash
# 运行冷却功能测试
go test -v ./internal/logic/logic_item -run TestItemCooldown
```

### 2. 集成测试

```bash
# 运行完整的道具操作测试
go test -v ./internal/logic/logic_item -run TestOperatePlayerItem
```

## 部署说明

### 1. Redis要求
- 确保Redis服务正常运行
- 建议配置Redis持久化以防数据丢失

### 2. 配置检查
- 检查Redis连接配置
- 确认冷却时间配置合理

### 3. 监控建议
- 监控Redis内存使用情况
- 监控冷却功能的错误率和响应时间

## 注意事项

1. **向后兼容**: 现有道具操作流程保持不变，冷却功能为可选增强
2. **性能影响**: 冷却检查会增加少量Redis访问，但采用异步设置减少影响
3. **数据一致性**: 使用Redis原子操作保证数据一致性
4. **错误恢复**: Redis连接失败时不影响主要业务流程，只记录日志
5. **扩展性**: 设计预留了配置和策略扩展接口，便于后续功能增强
