# server
rpc_server_name: hall
rpc_port: 12401

# 端口号
http_port: 22401
tcp_port: 32401

# 日志相关
log_level: trace
log_write: true
log_dir: ../../logs/hall
log_json: false

redis_addr: 192.168.1.58:6379
redis_passwd: 8888

redis_list:
  general:
    addr: 192.168.1.58:6379
    passwd: 8888
  player:
    addr:  192.168.1.58:6379
    passwd: 8888
  game:
    addr:  192.168.1.58:6379
    passwd: 8888
  lock:
    addr: 192.168.1.58:6379
    passwd: 8888

mysql_list:
  fancy_general:
    user: root
    addr: 192.168.1.58:3306
    passwd: fancydb2024#
    db: fancy_general

consul_addr: 192.168.1.58:8500
nsqd_addr: 192.168.1.58:4150
nsqd_http_addr: 192.168.1.58:4151
nsqlookupd_addrs:
  - 192.168.1.58:4161

# 屏蔽字库路径
forbid_config_path: ../forbid.txt

rpc_server_tags: normal

jwt:
  timeout: 876010
  secret: fancygame

kafka-producer:
  brokers: ["192.168.1.51:9092"]
  timeout: 10
